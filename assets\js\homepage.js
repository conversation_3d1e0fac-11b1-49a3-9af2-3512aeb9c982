// Homepage specific functionality
class Homepage {
    constructor(app) {
        this.app = app;
        this.init();
    }
    
    async init() {
        // Wait for app to be ready
        if (!this.app.videos.length) {
            setTimeout(() => this.init(), 100);
            return;
        }
        
        this.loadCategories();
        this.loadFeaturedVideos();
        this.loadPhotos();
        this.loadRecentVideos();
        this.setupFilterListeners();
    }
    
    loadCategories() {
        const categoriesGrid = document.getElementById('categoriesGrid');
        if (!categoriesGrid) return;
        
        categoriesGrid.innerHTML = '';
        
        this.app.categories.forEach(category => {
            const categoryCard = this.app.createCategoryCard(category);
            categoriesGrid.appendChild(categoryCard);
        });
    }
    
    loadFeaturedVideos() {
        const featuredVideos = document.getElementById('featuredVideos');
        if (!featuredVideos) return;

        featuredVideos.innerHTML = '';

        // Get top-rated videos only (no photos)
        const featured = this.app.videos
            .filter(video => video.type !== 'photo')
            .sort((a, b) => b.rating - a.rating)
            .slice(0, 6);

        featured.forEach(video => {
            const videoCard = this.app.createVideoCard(video);
            featuredVideos.appendChild(videoCard);
        });
    }
    
    loadPhotos() {
        const photosGrid = document.getElementById('photosGrid');
        if (!photosGrid) return;

        photosGrid.innerHTML = '';

        // Get all photos sorted by rating
        const photos = (this.app.newPhotos || [])
            .sort((a, b) => b.rating - a.rating)
            .slice(0, 12);

        photos.forEach(photo => {
            const photoCard = this.app.createVideoCard(photo);
            photosGrid.appendChild(photoCard);
        });
    }

    loadRecentVideos() {
        const recentVideos = document.getElementById('recentVideos');
        if (!recentVideos) return;

        recentVideos.innerHTML = '';

        // Get most recent videos only (no photos)
        const recent = this.app.videos
            .filter(video => video.type !== 'photo')
            .sort((a, b) => new Date(b.uploadDate) - new Date(a.uploadDate))
            .slice(0, 8);

        recent.forEach(video => {
            const videoCard = this.app.createVideoCard(video);
            recentVideos.appendChild(videoCard);
        });
    }

    setupFilterListeners() {
        const applyFiltersBtn = document.getElementById('applyFilters');
        const clearFiltersBtn = document.getElementById('clearFilters');

        if (applyFiltersBtn) {
            applyFiltersBtn.addEventListener('click', () => this.applyFilters());
        }

        if (clearFiltersBtn) {
            clearFiltersBtn.addEventListener('click', () => this.clearFilters());
        }
    }

    applyFilters() {
        const categoryFilter = document.getElementById('categoryFilter').value;
        const typeFilter = document.getElementById('typeFilter').value;
        const sortFilter = document.getElementById('sortFilter').value;
        const ratingFilter = parseFloat(document.getElementById('ratingFilter').value);

        const criteria = {
            category: categoryFilter,
            type: typeFilter,
            sortBy: sortFilter,
            minRating: ratingFilter,
            sortOrder: 'desc'
        };

        const filteredResults = this.app.advancedFilter(criteria);
        this.displayFilteredResults(filteredResults);
    }

    clearFilters() {
        // Reset all filter controls
        document.getElementById('categoryFilter').value = 'all';
        document.getElementById('typeFilter').value = 'all';
        document.getElementById('sortFilter').value = 'rating';
        document.getElementById('ratingFilter').value = '0';

        // Reload original content
        this.loadFeaturedVideos();
        this.loadPhotos();
        this.loadRecentVideos();
    }

    displayFilteredResults(results) {
        // Separate videos and photos from results
        const videos = results.filter(item => item.type !== 'photo');
        const photos = results.filter(item => item.type === 'photo');

        // Update featured videos section
        const featuredVideos = document.getElementById('featuredVideos');
        if (featuredVideos) {
            featuredVideos.innerHTML = '';

            if (videos.length === 0) {
                featuredVideos.innerHTML = '<p class="no-results">No videos match your filters.</p>';
            } else {
                // Show first 6 video results in featured section
                videos.slice(0, 6).forEach(video => {
                    const videoCard = this.app.createVideoCard(video);
                    featuredVideos.appendChild(videoCard);
                });
            }
        }

        // Update photos section
        const photosGrid = document.getElementById('photosGrid');
        if (photosGrid) {
            photosGrid.innerHTML = '';

            if (photos.length === 0) {
                photosGrid.innerHTML = '<p class="no-results">No photos match your filters.</p>';
            } else {
                // Show first 12 photo results
                photos.slice(0, 12).forEach(photo => {
                    const photoCard = this.app.createVideoCard(photo);
                    photosGrid.appendChild(photoCard);
                });
            }
        }

        // Update recent videos section
        const recentVideos = document.getElementById('recentVideos');
        if (recentVideos) {
            recentVideos.innerHTML = '';

            if (videos.length <= 6) {
                recentVideos.innerHTML = '<p class="no-results">No additional videos to show.</p>';
            } else {
                // Show next 8 video results in recent section
                videos.slice(6, 14).forEach(video => {
                    const videoCard = this.app.createVideoCard(video);
                    recentVideos.appendChild(videoCard);
                });
            }
        }
    }
}

// Initialize homepage when app is ready
document.addEventListener('DOMContentLoaded', () => {
    const initHomepage = () => {
        if (window.app && window.app.videos.length > 0) {
            new Homepage(window.app);
        } else {
            setTimeout(initHomepage, 100);
        }
    };
    
    initHomepage();
});
