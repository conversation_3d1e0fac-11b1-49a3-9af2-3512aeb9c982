// Homepage specific functionality
class Homepage {
    constructor(app) {
        this.app = app;
        this.init();
    }
    
    async init() {
        // Wait for app to be ready
        if (!this.app.videos.length) {
            setTimeout(() => this.init(), 100);
            return;
        }
        
        this.loadCategories();
        this.loadFeaturedVideos();
        this.loadRecentVideos();
        this.setupFilterListeners();
    }
    
    loadCategories() {
        const categoriesGrid = document.getElementById('categoriesGrid');
        if (!categoriesGrid) return;
        
        categoriesGrid.innerHTML = '';
        
        this.app.categories.forEach(category => {
            const categoryCard = this.app.createCategoryCard(category);
            categoriesGrid.appendChild(categoryCard);
        });
    }
    
    loadFeaturedVideos() {
        const featuredVideos = document.getElementById('featuredVideos');
        if (!featuredVideos) return;

        featuredVideos.innerHTML = '';

        // Get top-rated videos as featured (including both videos and photos)
        const allMedia = [...this.app.videos, ...(this.app.newPhotos || [])];
        const featured = allMedia
            .sort((a, b) => b.rating - a.rating)
            .slice(0, 6);

        featured.forEach(media => {
            const mediaCard = this.app.createVideoCard(media);
            featuredVideos.appendChild(mediaCard);
        });
    }
    
    loadRecentVideos() {
        const recentVideos = document.getElementById('recentVideos');
        if (!recentVideos) return;

        recentVideos.innerHTML = '';

        // Get most recent videos and photos
        const allMedia = [...this.app.videos, ...(this.app.newPhotos || [])];
        const recent = allMedia
            .sort((a, b) => new Date(b.uploadDate) - new Date(a.uploadDate))
            .slice(0, 8);

        recent.forEach(media => {
            const mediaCard = this.app.createVideoCard(media);
            recentVideos.appendChild(mediaCard);
        });
    }

    setupFilterListeners() {
        const applyFiltersBtn = document.getElementById('applyFilters');
        const clearFiltersBtn = document.getElementById('clearFilters');

        if (applyFiltersBtn) {
            applyFiltersBtn.addEventListener('click', () => this.applyFilters());
        }

        if (clearFiltersBtn) {
            clearFiltersBtn.addEventListener('click', () => this.clearFilters());
        }
    }

    applyFilters() {
        const categoryFilter = document.getElementById('categoryFilter').value;
        const typeFilter = document.getElementById('typeFilter').value;
        const sortFilter = document.getElementById('sortFilter').value;
        const ratingFilter = parseFloat(document.getElementById('ratingFilter').value);

        const criteria = {
            category: categoryFilter,
            type: typeFilter,
            sortBy: sortFilter,
            minRating: ratingFilter,
            sortOrder: 'desc'
        };

        const filteredResults = this.app.advancedFilter(criteria);
        this.displayFilteredResults(filteredResults);
    }

    clearFilters() {
        // Reset all filter controls
        document.getElementById('categoryFilter').value = 'all';
        document.getElementById('typeFilter').value = 'all';
        document.getElementById('sortFilter').value = 'rating';
        document.getElementById('ratingFilter').value = '0';

        // Reload original content
        this.loadFeaturedVideos();
        this.loadRecentVideos();
    }

    displayFilteredResults(results) {
        // Update featured section with filtered results
        const featuredVideos = document.getElementById('featuredVideos');
        if (featuredVideos) {
            featuredVideos.innerHTML = '';

            if (results.length === 0) {
                featuredVideos.innerHTML = '<p class="no-results">No content matches your filters.</p>';
                return;
            }

            // Show first 6 results in featured section
            results.slice(0, 6).forEach(media => {
                const mediaCard = this.app.createVideoCard(media);
                featuredVideos.appendChild(mediaCard);
            });
        }

        // Update recent section with remaining results
        const recentVideos = document.getElementById('recentVideos');
        if (recentVideos) {
            recentVideos.innerHTML = '';

            // Show next 8 results in recent section
            results.slice(6, 14).forEach(media => {
                const mediaCard = this.app.createVideoCard(media);
                recentVideos.appendChild(mediaCard);
            });
        }
    }
}

// Initialize homepage when app is ready
document.addEventListener('DOMContentLoaded', () => {
    const initHomepage = () => {
        if (window.app && window.app.videos.length > 0) {
            new Homepage(window.app);
        } else {
            setTimeout(initHomepage, 100);
        }
    };
    
    initHomepage();
});
